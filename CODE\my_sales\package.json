{"name": "my_sales", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "ts-node-dev --inspect --transpile-only --ignore-watch node_mdules src/server.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "description": "", "devDependencies": {"@types/node": "^24.0.3", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "eslint": "^9.29.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}