import express from 'express';
import 'reflect-metadata';
import 'express-async-errors';
import cors from 'cors';

import routes from './routes';
import ErrorHandledMiddleware from '../middlewares/ErrorHandledMiddlewares';
import AppDataSource from '@shared/typeorm/data-sources';

AppDataSource.initialize()
  .then(() => {
    console.log('Data Source has been initialized!');
  })
  .catch((err) => {
    console.error('Error during Data Source initialization', err);
  });

const app = express();

app.use(cors());
app.use(express.json());

app.use(routes);
app.use(ErrorHandledMiddleware.handleError);

console.log('Connected to the database!');

app.listen(3333, () => {
  console.log('Server started on port 3333');
});
